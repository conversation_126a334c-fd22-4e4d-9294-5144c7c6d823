import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:yo_merchant_payment/models/api_request.dart';
import 'package:yo_merchant_payment/models/response_data.dart';

class ApiServices {
  Future<void> sendData(ApiRequest apiRequest) async {
    final response =  await http.post(
      Uri.parse('https://devweb.yo.co.ug/172-31-28-190/zeroratedsitesimulator/execute'),
      headers: {
        'Content-Type': 'appliction/json; charset=UTF-8',
        'x-ympfg-apikey': '100000',
        'x-ympfg-nonce':nonce,
        'x-ympfg-signature': signatureBase64,
        'x-ympfg-hmac-sha256': hmacHex
      },
      body: jsonEncode(apiRequest.toJson()),
    );
    if (response.statusCode == 201) {
      print('Data created successfully');
    } else {
      print('Failed to create Data');
    }
  }
}
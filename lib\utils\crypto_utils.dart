import 'dart:convert';
import 'dart:typed_data';
import 'package:intl/intl.dart';
import 'package:pointycastle/export.dart';
import 'package:asn1lib/asn1lib.dart';
import 'package:uuid/uuid.dart';

RSAPrivateKey parsePrivateKeyFromPem(String pem) {
  final rows = pem
      .replaceAll('-----BEGIN PRIVATE KEY-----', '')
      .replaceAll('-----END PRIVATE KEY-----', '')
      .replaceAll('\n', '')
      .replaceAll('\r', '');

  final derBytes = base64.decode(rows);

  final asn1Parser = ASN1Parser(derBytes);
  final topLevelSeq = asn1Parser.nextObject() as ASN1Sequence;
  final privateKeyOctetString = topLevelSeq.elements[2] as ASN1OctetString;

  final pkcs1Parser = ASN1Parser(privateKeyOctetString.valueBytes());
  final pkSeq = pkcs1Parser.nextObject() as ASN1Sequence; 

  final modulus = pkSeq.elements[1] as ASN1Integer;
  final privateExponent = pkSeq.elements[3] as ASN1Integer;
  final p = pkSeq.elements[4] as ASN1Integer;
  final q = pkSeq.elements[5] as ASN1Integer;

  return RSAPrivateKey(
    modulus.valueAsBigInteger,
    privateExponent.valueAsBigInteger,
    p.valueAsBigInteger,
    q.valueAsBigInteger
  );
   
}

String getDateTime() {
  final now = DateTime.now();
  final formatter = DateFormat('yyy-MM-dd HH:mm:ss');
  return formatter.format(now);
}

String generateNonce() {
  var uuid = Uuid();
  return uuid.v4().replaceAll('-', '').substring(0,16);
}

Future<Uint8List> signData(Uint8List dataToSign, RSAPrivateKey privateKey) async {
  final signer = RSASigner(SHA256Digest(), '0609608648016503040201');

  signer.init(true, PrivateKeyParameter<RSAPrivateKey>(privateKey));

  final sig = signer.generateSignature(dataToSign);

  return sig.bytes;
}



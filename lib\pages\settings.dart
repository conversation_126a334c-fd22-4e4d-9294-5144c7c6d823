
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:yo_merchant_payment/models/user_settings.dart';
import 'package:yo_merchant_payment/pages/home_page.dart';


class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  Future<UserSettings?>?  futureSettings;
  final TextEditingController _ussdServiceCodeController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();

  String generateSessionId() {
    final Uuid uuid = Uuid();
    String v4 = uuid.v4();
    return v4.substring(0,16); 
  }

  void _addSettings() {
    final ussdServiceCode = _ussdServiceCodeController.text.trim();
    final phoneNumber = _phoneNumberController.text.trim();
    final sessionId = generateSessionId();

    if (ussdServiceCode.isEmpty || phoneNumber.isEmpty) return;

    final userSettings = UserSettings(
      sessionId: sessionId,
      ussdServiceCode: ussdServiceCode,
      phoneNumber: phoneNumber
    );

    Navigator.pushNamed(
      context, 
      HomePage.routeName,
      arguments: userSettings
      );

   
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        title: Text('Settings'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _ussdServiceCodeController,
              decoration: InputDecoration(labelText: 'USSD Service Code'),
            ),

            TextField(
              controller: _phoneNumberController,
              decoration: InputDecoration(labelText: 'Phone Number'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _addSettings, 
              child: const Text('Save')
            )

          ],
        ),
      ),
    );
  }
}
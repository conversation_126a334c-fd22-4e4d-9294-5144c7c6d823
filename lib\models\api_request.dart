import 'package:yo_merchant_payment/models/user_settings.dart';

class ApiRequest {
  final UserSettings userSettings;
  final String transactionTime;
  final String userInput;
  final bool isResponse;

  const ApiRequest({required this.userSettings, required this.transactionTime, required this.userInput,  this.isResponse = false});
  

  Map<String, dynamic> toJson() {
    final map = <String, dynamic> {};
    map['sessionId'] = userSettings.sessionId;
    map['serviceCode'] = userSettings.ussdServiceCode;
    map['customerPaymentAccount'] = userSettings.phoneNumber;
    map['customerNotificationMsisdn'] = userSettings.phoneNumber;
    map['transactionTime'] = transactionTime;
    map['userInputString'] = userInput;
    map['isResponse'] = isResponse? 'yes' : 'no';
    return map;
  }
  
  
  

  


}
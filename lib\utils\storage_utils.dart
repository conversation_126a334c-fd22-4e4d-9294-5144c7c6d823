import 'package:flutter_secure_storage/flutter_secure_storage.dart';

AndroidOptions _getAndroidOptions() => const AndroidOptions(
  encryptedSharedPreferences: true
);

final storage = FlutterSecureStorage(aOptions: _getAndroidOptions());

const String privateKeyStorageKey = 'private_key';

Future<void> savePrivateKey(String privateKeyPem) async {
  await storage.write(key: privateKeyStorageKey, value: privateKeyPem);
}

Future<String?> loadPrivateKey() async {
  return await storage.read(key: privateKeyStorageKey);
}
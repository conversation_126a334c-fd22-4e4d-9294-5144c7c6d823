import 'package:flutter/material.dart';
import 'package:yo_merchant_payment/pages/home_page.dart';
import 'package:yo_merchant_payment/pages/settings.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Flutter Demo',
      theme: ThemeData(
        
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      initialRoute: '/',

      routes: {
        HomePage.routeName:(context) => const HomePage(),
        '/add-settings': (ctx) => SettingsPage(),
      },
    );
  }
}

